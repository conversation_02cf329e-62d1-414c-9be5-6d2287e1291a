const fs = require('fs');
const path = require('path');

// Files to fix with their formatter patterns
const filesToFix = [
  'src/components/analytics/customer/CustomerOrderCharts.tsx',
  'src/components/analytics/DirectChartsExamples.tsx',
  'src/components/analytics/DirectECharts.tsx',
  'src/components/analytics/driver/DriverEarningsCharts.tsx',
  'src/components/analytics/vendor/VendorSalesCharts.tsx'
];

// Pattern to find and replace
const patterns = [
  {
    // Remove complex type imports
    find: /import { ([^}]*), TooltipFormatterCallback, TopLevelFormatterParams([^}]*) } from/g,
    replace: 'import { $1$2 } from'
  },
  {
    // Remove standalone type imports
    find: /import { TooltipFormatterCallback, TopLevelFormatterParams } from/g,
    replace: 'import {'
  },
  {
    // Fix complex formatter patterns
    find: /formatter: \(\(params: TopLevelFormatterParams\) => \{[\s\S]*?\}\) as TooltipFormatterCallback<TopLevelFormatterParams>,/g,
    replace: (match) => {
      const content = match.match(/\{([\s\S]*?)\}/)[1];
      return `// eslint-disable-next-line @typescript-eslint/no-explicit-any\n      formatter: (params: any) => {${content}},`;
    }
  },
  {
    // Fix simple formatter patterns
    find: /formatter: \(\(params: TopLevelFormatterParams\) => ([^)]+)\) as LabelFormatterCallback<TopLevelFormatterParams>,/g,
    replace: '// eslint-disable-next-line @typescript-eslint/no-explicit-any\n        formatter: (params: any) => $1,'
  }
];

filesToFix.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    patterns.forEach(pattern => {
      content = content.replace(pattern.find, pattern.replace);
    });
    
    // Additional manual fixes for specific patterns
    content = content.replace(
      /const data = Array\.isArray\(params\) \? params\[0\] : params;/g,
      'const data = params[0];'
    );
    
    fs.writeFileSync(filePath, content);
    console.log(`Fixed: ${filePath}`);
  }
});

console.log('All files fixed!');
